import app_store_image from "@/assets/images/app_store.svg";
import google_play_image from "@/assets/images/google_play.svg";
import iphone_15 from "@/assets/images/iphone 15.png";
import iphone_16 from "@/assets/images/iphone 16.png";
import { QRCodeCanvas } from "qrcode.react";

export default function ExplorerApp() {
  return (
    <section className="bg-gradient-to-b from-[#024344] to-[#002C2D] py-10 sm:py-16 md:py-20">
      <div className="container grid grid-cols-1 gap-4 bg-[#00000033] p-4 sm:p-6 md:p-8 lg:grid-cols-2 lg:p-10">
        <div className="flex flex-col gap-3 text-white sm:gap-4 md:gap-5">
          <h2 className="text-xl font-[700] sm:text-2xl md:text-3xl lg:text-[40px]">
            تطبيق مستكشف الغابات
          </h2>
          <p className="text-sm leading-6 font-[400] sm:text-base sm:leading-7 md:text-lg md:leading-8 lg:text-[24px]">
            يعرض تفاصيل الغابات المحددة على الخرائط، بما في ذلك الحدود،
            الإحداثيات، مصادر المياه، خطوط المياه، شبكة الري، وخطوط التنفيذ.
          </p>
          <div className="flex flex-wrap items-center gap-3 sm:gap-4 md:gap-5">
            <img
              src={app_store_image}
              alt=""
              className="h-[45px] sm:h-[55px] md:h-[65px]"
            />
            <img
              src={google_play_image}
              alt=""
              className="h-[45px] sm:h-[55px] md:h-[65px]"
            />
            <QRCodeCanvas
              value="https://reactjs.org/"
              className="!h-[45px] !w-[45px] overflow-hidden rounded-lg border sm:!h-[55px] sm:!w-[55px] md:!h-[65px] md:!w-[65px]"
            />
          </div>
        </div>
        <div className="relative mt-6 flex justify-center lg:mt-0 lg:block">
          <div className="flex justify-center lg:absolute lg:inset-0">
            <img
              src={iphone_15}
              alt=""
              className="h-auto max-h-[200px] w-auto sm:max-h-[250px] md:max-h-[300px] lg:-mt-[50px] lg:max-h-none"
            />
            <img
              src={iphone_16}
              alt=""
              className="h-auto max-h-[200px] w-auto sm:max-h-[250px] md:max-h-[300px] lg:-mt-[100px] lg:max-h-none"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
