import Footer from "@/components/common/footer";
import Navbar from "@/components/common/navbar";
import ExplorerApp from "@/components/home/<USER>";
import Hero from "@/components/home/<USER>";
import LatestServices from "@/components/home/<USER>";
import Map from "@/components/home/<USER>";
import News from "@/components/home/<USER>";
import Partners from "@/components/home/<USER>";
import { useCallback, useEffect, useRef, useState } from "react";
import Slider from "react-slick";

import "slick-carousel/slick/slick-theme.css";
import "slick-carousel/slick/slick.css";

// Slide data for better maintainability
const SLIDES = [
  { id: "hero", component: Hero, label: "الرئيسية" },
  { id: "map", component: Map, label: "الخريطة" },
  { id: "services", component: LatestServices, label: "الخدمات" },
  { id: "news", component: News, label: "الأخبار" },
  { id: "explorer", component: ExplorerApp, label: "المستكشف" },
  { id: "partners", component: Partners, label: "الشركاء" },
  { id: "footer", component: Footer, label: "تواصل معنا" },
] as const;

/**
 * Custom Pagination Component (for React Slick)
 */
const CustomPagination = ({
  activeIndex,
  slides,
  onBulletClick,
}: {
  activeIndex: number;
  slides: typeof SLIDES;
  onBulletClick: (index: number) => void;
}) => {
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent, index: number) => {
      if (event.key === "Enter" || event.key === " ") {
        event.preventDefault();
        onBulletClick(index);
      }
    },
    [onBulletClick],
  );

  return (
    <div
      className="custom-pagination"
      role="navigation"
      aria-label="Slider navigation"
    >
      {slides.map((slide, index) => (
        <button
          key={slide.id}
          className={`slick-pagination-bullet ${
            index === activeIndex ? "slick-pagination-bullet-active" : ""
          }`}
          onClick={() => onBulletClick(index)}
          onKeyDown={(e) => handleKeyDown(e, index)}
          data-slide={slide.label}
          aria-label={`Go to slide ${index + 1}: ${slide.label}`}
          aria-current={index === activeIndex ? "true" : "false"}
          tabIndex={0}
        />
      ))}
    </div>
  );
};

export default function HomePageSlick() {
  const sliderRef = useRef<Slider>(null);
  const [activeSlide, setActiveSlide] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Handle wheel navigation
  const handleWheel = useCallback((e: WheelEvent) => {
    if (!sliderRef.current) return;

    e.preventDefault();

    if (e.deltaY > 0) {
      sliderRef.current.slickNext();
    } else {
      sliderRef.current.slickPrev();
    }
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!sliderRef.current) return;

    switch (e.key) {
      case "ArrowDown":
      case "PageDown":
        e.preventDefault();
        sliderRef.current.slickNext();
        break;
      case "ArrowUp":
      case "PageUp":
        e.preventDefault();
        sliderRef.current.slickPrev();
        break;
      case "Home":
        e.preventDefault();
        sliderRef.current.slickGoTo(0);
        break;
      case "End":
        e.preventDefault();
        sliderRef.current.slickGoTo(SLIDES.length - 1);
        break;
    }
  }, []);

  useEffect(() => {
    document.body.classList.add("carousel-active");
    document.body.style.overflow = "hidden";

    // Add event listeners
    window.addEventListener("wheel", handleWheel, { passive: false });
    window.addEventListener("keydown", handleKeyDown);

    // Simulate loading completion
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => {
      document.body.classList.remove("carousel-active");
      document.body.style.overflow = "auto";
      window.removeEventListener("wheel", handleWheel);
      window.removeEventListener("keydown", handleKeyDown);
      clearTimeout(timer);
    };
  }, [handleWheel, handleKeyDown]);

  const settings = {
    dots: false,
    infinite: false,
    speed: 1000,
    slidesToShow: 1,
    slidesToScroll: 1,
    vertical: true,
    verticalSwiping: true,
    arrows: false,
    accessibility: true,
    focusOnSelect: false,
    pauseOnHover: false,
    pauseOnFocus: false,
    beforeChange: (_current: number, next: number) => setActiveSlide(next),
    onInit: () => setHasError(false),
    onReInit: () => setHasError(false),
  };

  const handleBulletClick = useCallback((index: number) => {
    if (sliderRef.current) {
      sliderRef.current.slickGoTo(index);
    }
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <div className="slider-loading">
        <div className="spinner"></div>
        <p className="mt-4">جاري التحميل...</p>
      </div>
    );
  }

  // Error state
  if (hasError) {
    return (
      <div className="slider-loading">
        <p>حدث خطأ في تحميل الصفحة</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 rounded bg-white px-4 py-2 text-black"
        >
          إعادة المحاولة
        </button>
      </div>
    );
  }

  return (
    <>
      <Navbar />

      <CustomPagination
        activeIndex={activeSlide}
        slides={SLIDES}
        onBulletClick={handleBulletClick}
      />

      <Slider
        ref={sliderRef}
        {...settings}
        className="home-vertical-slick"
        onSwipe={(direction) => {
          // Handle swipe for better mobile experience
          if (direction === "up") {
            sliderRef.current?.slickNext();
          } else if (direction === "down") {
            sliderRef.current?.slickPrev();
          }
        }}
      >
        {SLIDES.map((slide, index) => {
          const Component = slide.component;
          return (
            <div key={slide.id} className="h-screen">
              <Component />
            </div>
          );
        })}
      </Slider>
    </>
  );
}
