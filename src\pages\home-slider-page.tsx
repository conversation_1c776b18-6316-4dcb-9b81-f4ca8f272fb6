import Footer from "@/components/common/footer";
import Navbar from "@/components/common/navbar";
import ExplorerApp from "@/components/home/<USER>";
import Hero from "@/components/home/<USER>";
import LatestServices from "@/components/home/<USER>";
import Map from "@/components/home/<USER>";
import News from "@/components/home/<USER>";
import Partners from "@/components/home/<USER>";
import { useEffect, useRef, useState } from "react";
import Slider from "react-slick";

import "slick-carousel/slick/slick-theme.css";
import "slick-carousel/slick/slick.css";

/**
 * Custom Pagination Component (for React Slick)
 */
const CustomPagination = ({
  activeIndex,
  totalSlides,
  onBulletClick,
}: {
  activeIndex: number;
  totalSlides: number;
  onBulletClick: (index: number) => void;
}) => {
  return (
    <div className="custom-pagination">
      {Array.from({ length: totalSlides }, (_, index) => (
        <span
          key={index}
          className={`slick-pagination-bullet ${
            index === activeIndex ? "slick-pagination-bullet-active" : ""
          }`}
          onClick={() => onBulletClick(index)}
          data-slide={index + 1}
        />
      ))}
    </div>
  );
};

export default function HomePageSlick() {
  const sliderRef = useRef<any>(null);
  const [activeSlide, setActiveSlide] = useState(0);

  useEffect(() => {
    document.body.classList.add("carousel-active");
    document.body.style.overflow = "hidden"; // disable scroll

    const handleWheel = (e: WheelEvent) => {
      if (!sliderRef.current) return;
      if (e.deltaY > 0) {
        sliderRef.current.slickNext();
      } else {
        sliderRef.current.slickPrev();
      }
    };

    window.addEventListener("wheel", handleWheel, { passive: false });

    return () => {
      document.body.classList.remove("carousel-active");
      document.body.style.overflow = "auto";
      window.removeEventListener("wheel", handleWheel);
    };
  }, []);

  const settings = {
    dots: false,
    infinite: false,
    speed: 1000,
    slidesToShow: 1,
    slidesToScroll: 1,
    vertical: true,
    verticalSwiping: true,
    arrows: false,
    beforeChange: (_: any, __: any, next: number) => setActiveSlide(next),
  };

  const handleBulletClick = (index: number) => {
    if (sliderRef.current) {
      sliderRef.current.slickGoTo(index);
    }
  };

  return (
    <>
      <Navbar />

      <CustomPagination
        activeIndex={activeSlide}
        totalSlides={6}
        onBulletClick={handleBulletClick}
      />

      <Slider ref={sliderRef} {...settings} className="home-vertical-slick">
        <div className="h-screen">
          <Hero />
        </div>
        <div className="h-screen">
          <Map />
        </div>
        <div className="h-screen">
          <LatestServices />
        </div>
        <div className="h-screen">
          <News />
        </div>
        <div className="h-screen">
          <ExplorerApp />
        </div>
        <div className="h-screen">
          <Partners />
        </div>
        <div className="h-screen">
          <Footer />
        </div>
      </Slider>
    </>
  );
}
