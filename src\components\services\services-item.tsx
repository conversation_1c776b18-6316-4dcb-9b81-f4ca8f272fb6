import type { Service } from "@/types";
import { URLS } from "@/utils/urls";
import { Link } from "react-router";

export default function ServicesItem({
  description,
  image,
  title,
  id,
}: Service) {
  return (
    <Link
      to={`${URLS.services}/${id}`}
      className="flex h-full flex-col gap-3 rounded-lg bg-white p-4 shadow transition-all duration-200 sm:gap-4 sm:p-5 md:p-6"
    >
      <img
        src={image}
        alt={title}
        className="h-8 w-8 object-cover sm:h-9 sm:w-9 md:h-[40px] md:w-[40px]"
      />
      <h3 className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-sm leading-6 font-[600] text-transparent sm:text-[15px] sm:leading-6 md:text-[16px] md:leading-7">
        {title}
      </h3>
      <p className="text-xs leading-4 font-[400] text-[#121217] sm:text-[13px] sm:leading-5 md:text-[14px]">
        {description}
      </p>
    </Link>
  );
}
