import hero_video from "@/assets/videos/hero.mp4";

export default function Hero() {
  return (
    <section className="relative h-screen w-screen">
      <video
        autoPlay
        loop
        muted
        className=" -rotate-y-180 object-cover"
      >
        <source src={hero_video} type="video/mp4" />
      </video>
      <div className="absolute inset-0 bg-gradient-to-b from-[#000]/10 to-[#02302C]/90"></div>

      <div className="absolute top-1/2 left-1/2 container flex w-full max-w-4xl -translate-x-1/2 -translate-y-1/2 flex-col gap-4 px-4 text-center text-white sm:gap-6 sm:px-6 md:px-8">
        <h1 className="text-3xl font-[700] sm:text-4xl md:text-5xl lg:text-6xl xl:text-[72px]">
          نحمي لتستمر الحياة
        </h1>
        <p className="text-sm leading-6 font-[400] sm:text-base sm:leading-7 md:text-lg md:leading-8 lg:text-[20px]">
          نطوّر النظام الوطني لإدارة معلومات الغابات لرصد الغابات وتحليل
          بياناتها باستخدام تقنيات الاستشعار عن بعد ونظم المعلومات الجغرافية
          لمتابعة التغيرات البيئية ودعم قرارات الاستدامة عبر لوحات تفاعلية دقيقة
        </p>
      </div>
    </section>
  );
}
