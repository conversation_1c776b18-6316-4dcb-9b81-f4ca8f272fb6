import { BrowserRouter, Navigate, Route, Routes } from "react-router";
import BaseLayout from "./layouts/base-layout";
import PageLayout from "./layouts/page-layout";
import AboutPage from "./pages/about-page";
import LoginPage from "./pages/auth/login-page";
import ContactUsPage from "./pages/contact-us-page";
import DocumentationsPage from "./pages/documentations-page";
import FAQPage from "./pages/faq-page";
import HomeSliderPage from "./pages/home-slider-page";
import NotFoundPage from "./pages/not-found-page";
import QuickFactsPage from "./pages/quick-facts-page";
import ServiceDetailsPage from "./pages/services/service-details-page";
import ServicesPage from "./pages/services/services-page";
import { URLS } from "./utils/urls";

export default function App() {
  return (
    <div dir="rtl">
      <BrowserRouter basename="/GDF-portal">
        <Routes>
          <Route path={URLS.auth.login} element={<LoginPage />} />
          <Route path={URLS.home} element={<HomeSliderPage />} />
          <Route path={URLS.home} element={<BaseLayout />}>
            {/* <Route path={URLS.home} element={<HomePage />} /> */}
            <Route path={URLS.about} element={<AboutPage />} />
            <Route path={URLS.home} element={<PageLayout />}>
              <Route
                path={URLS.serviceDetails}
                element={<ServiceDetailsPage />}
              />
              <Route path={URLS.services} element={<ServicesPage />} />
              <Route path={URLS.contactUs} element={<ContactUsPage />} />
              <Route path={URLS.faq} element={<FAQPage />} />
              <Route path={URLS.quickFacts} element={<QuickFactsPage />} />
              <Route
                path={URLS.documentations}
                element={<DocumentationsPage />}
              />
            </Route>
          </Route>
          <Route path={URLS.notFound} element={<NotFoundPage />} />
          <Route path="*" element={<Navigate to={URLS.notFound} />} />
        </Routes>
      </BrowserRouter>
    </div>
  );
}
