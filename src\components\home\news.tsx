import hero_image from "@/assets/images/hero.jpg";
import mission_image from "@/assets/images/mission.jpg";
import { ArrowLeft, ArrowRight, ChevronLeft } from "lucide-react";
import { useRef, useState } from "react";
import Slider from "react-slick";

export default function News() {
  const sliderRef = useRef<Slider>(null);
  const [current, setCurrent] = useState(0);

  const settings = {
    dots: false,
    infinite: false,
    speed: 800,
    autoplay: true,
    autoplaySpeed: 4000,
    slidesToShow: 1,
    slidesToScroll: 1,
    initialSlide: 0,
    rtl: true,
    arrows: false, // we will make custom arrows
    beforeChange: (_: number, next: number) => setCurrent(next),
  };

  const totalSlides = 6;

  return (
    <section className="relative">
      <img
        src={hero_image}
        alt=""
        className="absolute -z-10 h-full w-full object-cover"
      />
      <div className="absolute inset-0 -z-10 bg-[#FFFFFFD9]" />

      <div className="relative container px-4 py-10 sm:px-6 sm:py-16 md:px-8 md:py-20">
        <h2 className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-2xl font-[600] text-transparent sm:text-3xl md:text-4xl lg:text-[48px]">
          الأخبار
        </h2>

        <Slider ref={sliderRef} {...settings} className="mt-6 sm:mt-8 md:mt-10">
          {[...new Array(totalSlides)].map((_, index) => {
            return (
              <div key={index}>
                <div className="grid grid-cols-1 gap-5 lg:grid-cols-2">
                  {/* Right Side Text */}
                  <div
                    dir="rtl"
                    className="flex flex-col gap-6 sm:gap-8 md:gap-10"
                  >
                    <div className="text-sm font-[600] text-[#6C6C89] sm:text-base md:text-lg lg:text-[20px]">
                      1 ديسمبر, 2024
                      <span className="mt-2 block h-[2px] w-[30px] bg-white sm:w-[40px] md:w-[50px]"></span>
                    </div>

                    <div className="flex flex-col gap-3 sm:gap-4 md:gap-5">
                      <h2 className="bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-lg font-[600] text-transparent sm:text-xl md:text-2xl lg:text-[30px]">
                        مبادرة السعودية الخضراء تطلق برنامجا ضخما لإعادة التشجير
                      </h2>
                      <div className="text-sm leading-6 sm:text-base sm:leading-7 md:text-lg md:leading-8">
                        أطلقت مبادرة السعودية الخضراء برنامجا واسع النطاق لإعادة
                        التشجير لمكافحة التصحر واستعادة النظم البيئية وتعزيز
                        الاستدامة في جميع أنحاء المملكة.
                      </div>
                    </div>

                    <div className="flex cursor-pointer items-center gap-1 bg-gradient-to-r from-[#035859] to-[#599379] bg-clip-text text-sm font-[500] text-transparent sm:text-base md:text-lg lg:text-[20px]">
                      التفاصيل
                      <ChevronLeft
                        color="#307D7E"
                        className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6"
                      />
                    </div>
                  </div>

                  {/* Left Side Image */}
                  <div className="hidden lg:block">
                    <img
                      src={mission_image}
                      alt=""
                      className="h-[300px] max-w-full rounded-lg object-cover sm:h-[400px] md:h-[500px]"
                    />
                  </div>
                </div>
              </div>
            );
          })}
        </Slider>

        {/* Custom Arrows + Slide Counter */}
        <div className="absolute bottom-4 left-2 flex w-full max-w-[90%] flex-row-reverse items-center justify-between gap-2 px-2 sm:bottom-6 sm:left-4 sm:max-w-[80%] sm:gap-4 sm:px-4 md:bottom-8 md:left-5 md:max-w-[70%] md:gap-5 lg:bottom-10 lg:max-w-[50%] lg:px-10">
          <div className="flex items-center gap-2 sm:gap-3">
            {/* Prev Button */}
            <button
              onClick={() => sliderRef.current?.slickPrev()}
              disabled={current === 0} // 🔥 disable at first slide
              className="cursor-pointer rounded-full p-1 transition-colors hover:bg-white/10 sm:p-2"
            >
              <ArrowRight
                size={16}
                color={current === 0 ? "#6C6C89" : "#035859"}
                className="sm:h-5 sm:w-5 md:h-6 md:w-6"
              />
            </button>

            {/* Next Button */}
            <button
              onClick={() => sliderRef.current?.slickNext()}
              disabled={current === totalSlides - 1} // 🔥 disable at last slide
              className="cursor-pointer rounded-full p-1 transition-colors hover:bg-white/10 sm:p-2"
            >
              <ArrowLeft
                size={16}
                color={current === totalSlides - 1 ? "#6C6C89" : "#035859"}
                className="sm:h-5 sm:w-5 md:h-6 md:w-6"
              />
            </button>
          </div>

          {/* Slide Counter */}
          <span className="text-sm font-semibold text-[#035859] sm:text-base md:text-lg">
            {current + 1} - {totalSlides}
          </span>
        </div>
      </div>
    </section>
  );
}
