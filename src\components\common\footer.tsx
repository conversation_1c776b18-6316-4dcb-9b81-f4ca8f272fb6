import { URLS } from "@/utils/urls";
import { <PERSON> } from "react-router";

export default function Footer() {
  return (
    <footer className="bg-[#002C2D] py-6 sm:py-8 md:py-10">
      <div className="container grid grid-cols-1 gap-6 px-4 text-white sm:gap-8 sm:px-6 md:grid-cols-2 md:gap-10 md:px-8 lg:grid-cols-3 lg:px-12 xl:px-20">
        <div className="flex flex-col gap-3 sm:gap-4 md:gap-5">
          <div className="text-base font-[600] sm:text-lg md:text-[20px]">
            روابط مهمة
          </div>
          <div className="flex flex-col gap-2 text-xs font-[400] sm:gap-3 sm:text-sm md:text-[14px]">
            <Link
              target="_blank"
              to={"https://www.fao.org/home/<USER>"}
              className="transition-colors hover:text-gray-300"
            >
              منظمة الأغذية والزراعة
            </Link>
            <Link
              target="_blank"
              to={"https://www.mewa.gov.sa/ar/Pages/default.aspx"}
              className="transition-colors hover:text-gray-300"
            >
              وزارة البيئة والمياة والبلدية
            </Link>
            <Link
              target="_blank"
              to={"https://ncvc.gov.sa/ar/Pages/default.aspx"}
              className="transition-colors hover:text-gray-300"
            >
              المركز الوطنى لتنمية الغطاء النباتى ومكافحة التصحر
            </Link>
            <Link
              target="_blank"
              to={"https://ncvc.gov.sa/ar/HowCanWeHelp/SLA/Pages/Privacy.aspx"}
              className="transition-colors hover:text-gray-300"
            >
              سياسة الخصوصية
            </Link>
          </div>
        </div>
        <div className="flex flex-col gap-3 sm:gap-4 md:gap-5">
          <div className="text-base font-[600] sm:text-lg md:text-[20px]">
            روابط سريعة
          </div>
          <div className="flex flex-col gap-2 text-xs font-[400] sm:gap-3 sm:text-sm md:text-[14px]">
            <Link
              to={URLS.faq}
              className="transition-colors hover:text-gray-300"
            >
              الأسئلة شائعة
            </Link>
            <Link
              to={URLS.quickFacts}
              className="transition-colors hover:text-gray-300"
            >
              حقائق سريعة
            </Link>
          </div>
        </div>
        <div className="flex flex-col gap-3 sm:gap-4 md:gap-5">
          <div className="text-base font-[600] sm:text-lg md:text-[20px]">
            {" "}
            الخدمات{" "}
          </div>
          <div className="flex flex-col gap-2 text-xs font-[400] sm:gap-3 sm:text-sm md:text-[14px]">
            <Link
              target="_blank"
              to={"#"}
              className="transition-colors hover:text-gray-300"
            >
              مستكشف الغابات
            </Link>
          </div>
        </div>
      </div>

      <div className="container px-4 sm:px-6 md:px-8 lg:px-12 xl:px-20">
        <p className="mt-6 border-t pt-6 text-center text-xs font-[400] text-white sm:mt-8 sm:pt-8 sm:text-sm md:mt-10 md:pt-10 md:text-[14px]">
          جميع الحقوق محفوظة للمركز الوطنى لتنمية الغطاء النباتى ومكافحة التصحر
          <span className="font-bold"> © {new Date().getFullYear()}</span>
        </p>
      </div>
    </footer>
  );
}
