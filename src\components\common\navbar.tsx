import logo from "@/assets/images/logo-white.svg";
import { But<PERSON> } from "@/components/ui/button";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDescription,
  She<PERSON><PERSON><PERSON>er,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { navLinks } from "@/data/nav-links";
import useNavbar from "@/hooks/use-navbar";
import { cn } from "@/lib/utils";
import { URLS } from "@/utils/urls";
import { Globe, Menu, X } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router";

export default function Navbar() {
  const { pathname, transparentNavbar } = useNavbar();
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  const handleNavLinkClick = () => {
    setIsSheetOpen(false);
  };

  return (
    <nav
      className={cn(
        "top-0 left-0 z-[100] w-full bg-[#002C2D] py-4 text-white",
        pathname === URLS.home ? "fixed" : "sticky",
        transparentNavbar && pathname === URLS.home ? "bg-transparent" : "",
      )}
    >
      <div className="container flex items-center justify-between px-4 sm:px-6 md:px-8 lg:px-12 xl:px-20">
        <Link to={URLS.home}>
          <img
            src={logo}
            alt="GDF Portal Logo"
            className="h-6 sm:h-7 md:h-8 lg:h-auto"
          />
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden gap-8 text-white lg:flex">
          {navLinks.map(({ id, link, text }) => {
            return (
              <Link
                to={link}
                key={id}
                className="transition-colors hover:text-gray-300"
              >
                {text}
              </Link>
            );
          })}
        </div>

        {/* Desktop Actions */}
        <div className="hidden items-center gap-5 lg:flex">
          <Button
            variant={"outline"}
            className="cursor-pointer border-white !bg-transparent !text-white transition-colors hover:bg-white hover:text-[#002C2D]"
          >
            EN
            <Globe className="ml-2" />
          </Button>
        </div>

        {/* Mobile Menu Button */}
        <div className="flex items-center gap-3 lg:hidden">
          <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="p-2 text-white hover:bg-white/10"
              >
                <Menu className="h-6 w-6" />
              </Button>
            </SheetTrigger>
            <SheetContent
              side="left"
              className="z-[200] w-[320px] border-r border-white/10 bg-gradient-to-b from-[#002C2D] to-[#001A1B] p-0 backdrop-blur-sm"
            >
              <SheetHeader className="border-b border-white/10 bg-white/5 p-6">
                <SheetTitle className="sr-only">
                  Mobile Navigation Menu
                </SheetTitle>
                <SheetDescription className="sr-only">
                  Access main navigation, language settings, and portal
                  information
                </SheetDescription>
                <div className="flex items-center justify-between">
                  <Link
                    to={URLS.home}
                    onClick={handleNavLinkClick}
                    className="group"
                  >
                    <img
                      src={logo}
                      alt="Logo"
                      className="h-8 transition-transform group-hover:scale-105"
                    />
                  </Link>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="rounded-full p-2 text-white transition-all duration-200 hover:bg-white/20 hover:text-white"
                    onClick={() => setIsSheetOpen(false)}
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>
              </SheetHeader>

              {/* Navigation Links */}
              <div className="flex-1 p-6">
                <nav className="flex flex-col space-y-2">
                  {navLinks.map(({ id, link, text }) => (
                    <Link
                      to={link}
                      key={id}
                      className="group relative rounded-xl px-4 py-4 text-white transition-all duration-200 hover:bg-white/10 hover:text-gray-100 hover:shadow-lg hover:shadow-black/20"
                      onClick={handleNavLinkClick}
                    >
                      <span className="relative z-10 font-medium">{text}</span>
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-white/5 to-white/0 opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
                    </Link>
                  ))}
                </nav>
              </div>

              {/* Language Selector */}
              <div className="border-t border-white/10 bg-white/5 p-6">
                <Button
                  variant={"outline"}
                  className="group w-full cursor-pointer rounded-xl border-white/20 !bg-white/5 !text-white transition-all duration-200 hover:border-white/40 hover:!bg-white/20 hover:shadow-lg hover:shadow-white/10"
                >
                  <span className="font-medium">EN</span>
                  <Globe className="ml-2 h-4 w-4 transition-transform group-hover:rotate-12" />
                </Button>
              </div>

              {/* Footer */}
              <div className="border-t border-white/10 bg-white/5 p-6">
                <div className="text-center">
                  <div className="text-sm font-medium text-white/60">
                    © 2025 GDF Portal
                  </div>
                  <div className="mt-2 text-xs text-white/40">
                    Empowering Digital Future
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>
  );
}
