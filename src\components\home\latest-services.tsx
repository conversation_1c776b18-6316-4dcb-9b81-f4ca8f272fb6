import hero_image from "@/assets/images/hero.jpg";
import { servicesList } from "@/data/services-list";
import { URLS } from "@/utils/urls";
import { Link } from "react-router";
import Slider from "react-slick";
import ServicesItem from "../services/services-item";
import { Button } from "../ui/button";

export default function LatestServices() {
  const settings = {
    dots: false,
    infinite: true,
    speed: 800,
    autoplay: true,
    autoplaySpeed: 4000,
    slidesToShow: 4,
    slidesToScroll: 1,
    rtl: true,
    arrows: false,
    responsive: [
      {
        breakpoint: 1280, // <= 1280px
        settings: { slidesToShow: 3, slidesToScroll: 1 },
      },
      {
        breakpoint: 1024, // <= 1024px (تابلت)
        settings: { slidesToShow: 2, slidesToScroll: 1 },
      },
      {
        breakpoint: 640, // <= 640px (موبايل)
        settings: { slidesToShow: 1, slidesToScroll: 1 },
      },
    ],
  };

  return (
    <section className="relative">
      <img
        src={hero_image}
        alt=""
        className="absolute -z-1 h-full w-full object-cover"
      />
      <div className="absolute inset-0 -z-1 bg-gradient-to-b from-[#fff] to-[#000]/50" />

      <div className="container px-4 py-10 sm:px-6 sm:py-16 md:px-8 md:py-20">
        <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center sm:gap-0">
          <h2 className="text-xl font-[700] text-white sm:text-2xl md:text-3xl lg:text-[30px]">
            أحدث الخدمات
          </h2>
          <Link to={URLS.services}>
            <Button
              variant={"outline"}
              className="cursor-pointer !bg-transparent text-sm !text-white transition-colors hover:bg-white hover:!text-[#002C2D] sm:text-base"
            >
              عرض الكل
            </Button>
          </Link>
        </div>

        <Slider {...settings} className="mt-10 h-full">
          {servicesList.map((service) => (
            <div className="h-full px-2" dir="rtl" key={service.id}>
              <ServicesItem {...service} />
            </div>
          ))}
        </Slider>
      </div>
    </section>
  );
}
